package com.aetrust.repositories;

import com.aetrust.models.User;
import com.aetrust.types.Types.*;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    Optional<User> findByEmailAndDeletedAtIsNull(String email);
    Optional<User> findByPhoneAndDeletedAtIsNull(String phone);
    Optional<User> findByUsernameAndDeletedAtIsNull(String username);
    Optional<User> findByUserUuidAndDeletedAtIsNull(UUID userUuid);
    Optional<User> findByEmailAndDeletedAtIsNullAndAccountStatusNot(String email, AccountStatus accountStatus);
    Optional<User> findByPhoneAndDeletedAtIsNullAndAccountStatusNot(String phone, AccountStatus accountStatus);

    boolean existsByEmailAndDeletedAtIsNull(String email);
    boolean existsByPhoneAndDeletedAtIsNull(String phone);
    boolean existsByUsernameAndDeletedAtIsNull(String username);
    boolean existsByUserUuidAndDeletedAtIsNull(UUID userUuid);

    List<User> findByAccountStatusAndDeletedAtIsNull(AccountStatus accountStatus);
    List<User> findByKycStatusAndDeletedAtIsNull(KycStatus kycStatus);
    List<User> findByRoleAndDeletedAtIsNull(UserRole role);
    List<User> findByIsVerifiedAndDeletedAtIsNull(boolean isVerified);
    List<User> findByRegistrationStepAndDeletedAtIsNull(RegistrationStep registrationStep);

    List<User> findByCreatedAtAfterAndDeletedAtIsNull(LocalDateTime date);
    List<User> findByCreatedAtBetweenAndDeletedAtIsNull(LocalDateTime startDate, LocalDateTime endDate);

    @Query("SELECT u FROM User u WHERE u.role = :role AND u.deletedAt IS NULL")
    List<User> findUsersByRole(@Param("role") UserRole role);

    @Query("SELECT u FROM User u WHERE u.deletedAt IS NULL ORDER BY u.createdAt DESC")
    List<User> findAllActiveUsers();

    @Query("SELECT u FROM User u WHERE " +
           "(LOWER(u.email) LIKE LOWER(CONCAT('%', :search, '%')) " +
           "OR LOWER(u.phone) LIKE LOWER(CONCAT('%', :search, '%')) " +
           "OR LOWER(u.username) LIKE LOWER(CONCAT('%', :search, '%'))) " +
           "AND u.deletedAt IS NULL")
    List<User> searchUsers(@Param("search") String search);

    @Query("SELECT COUNT(u) FROM User u WHERE u.role = :role AND u.deletedAt IS NULL")
    long countByRole(@Param("role") UserRole role);

    @Query("SELECT COUNT(u) FROM User u WHERE u.accountStatus = :status AND u.deletedAt IS NULL")
    long countByAccountStatus(@Param("status") AccountStatus status);

    @Query("SELECT COUNT(u) FROM User u WHERE u.kycStatus = :status AND u.deletedAt IS NULL")
    long countByKycStatus(@Param("status") KycStatus status);

    // Soft delete
    @Modifying
    @Query("UPDATE User u SET u.deletedAt = :deletedAt WHERE u.id = :id")
    void softDeleteById(@Param("id") Long id, @Param("deletedAt") LocalDateTime deletedAt);

    @Modifying
    @Query("UPDATE User u SET u.deletedAt = :deletedAt WHERE u.userUuid = :userUuid")
    void softDeleteByUuid(@Param("userUuid") UUID userUuid, @Param("deletedAt") LocalDateTime deletedAt);
}
