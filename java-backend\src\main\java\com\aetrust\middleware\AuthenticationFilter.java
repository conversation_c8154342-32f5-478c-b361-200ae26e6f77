package com.aetrust.middleware;

import com.aetrust.services.SecurityService;
import com.aetrust.utils.JwtUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@Slf4j
@Component
public class AuthenticationFilter extends OncePerRequestFilter {
    
    @Autowired
    private JwtUtils jwtUtils;
    
    @Autowired
    private SecurityService securityService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        String requestPath = request.getRequestURI();
        String method = request.getMethod();
        String ipAddress = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");

        // Debug logging to see actual path
        log.info("AuthenticationFilter - Request Path: '{}', Method: '{}'", requestPath, method);

        if (isPublicEndpoint(requestPath, method)) {
            log.info("AuthenticationFilter - Path '{}' is public, allowing access", requestPath);
            filterChain.doFilter(request, response);
            return;
        }

        log.info("AuthenticationFilter - Path '{}' is protected, checking authentication", requestPath);
        
        try {
            // rate limiting check
            SecurityService.RateLimitResult rateLimitResult = securityService.checkRateLimit(
                ipAddress, "ip", requestPath);
            
            if (!rateLimitResult.isAllowed()) {
                sendErrorResponse(response, 429, "rate limit exceeded", 
                    Map.of("retryAfter", rateLimitResult.getResetTime()));
                return;
            }
            
            String token = extractToken(request);
            if (token == null) {
                securityService.recordFailedAttempt(ipAddress, "ip");
                sendErrorResponse(response, 401, "access token required", null);
                return;
            }
            
            JwtUtils.UserPayload userPayload = jwtUtils.extractUserPayload(token);
            
            SecurityService.SessionContext sessionContext = new SecurityService.SessionContext(
                ipAddress, userAgent, userPayload.getIat());
            
            SecurityService.SessionValidationResult sessionResult = 
                securityService.validateSessionIntegrity(userPayload.getId(), sessionContext);
            
            if (!sessionResult.isValid()) {
                handleSessionValidationFailure(response, sessionResult.getAction());
                return;
            }
            
            SecurityService.ActivityContext activityContext = new SecurityService.ActivityContext(
                ipAddress, userAgent, System.currentTimeMillis());
            
            SecurityService.SuspiciousActivityResult suspiciousResult = 
                securityService.detectSuspiciousActivity(userPayload.getId(), activityContext);
            
            if (suspiciousResult.isSuspicious()) {
                handleSuspiciousActivity(response, suspiciousResult);
                return;
            }
            
            SecurityService.BruteForceResult bruteForceResult = 
                securityService.checkBruteForceProtection(userPayload.getId(), "user");
            
            if (!bruteForceResult.isAllowed()) {
                sendErrorResponse(response, 429, bruteForceResult.getReason(), 
                    Map.of("lockoutTime", bruteForceResult.getLockoutTime()));
                return;
            }
            
            List<SimpleGrantedAuthority> authorities = List.of(
                new SimpleGrantedAuthority("ROLE_" + userPayload.getRole().name())
            );
            
            UsernamePasswordAuthenticationToken authentication = 
                new UsernamePasswordAuthenticationToken(userPayload, null, authorities);
            
            SecurityContextHolder.getContext().setAuthentication(authentication);
            
            securityService.clearFailedAttempts(ipAddress, "ip");
            securityService.clearFailedAttempts(userPayload.getId(), "user");
            
            request.setAttribute("user", userPayload);
            request.setAttribute("startTime", System.currentTimeMillis());
            
            filterChain.doFilter(request, response);
            
        } catch (Exception error) {
            handleAuthenticationError(request, response, error);
        }
    }
    
    private String extractToken(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        return null;
    }
    
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
    
    private boolean isPublicEndpoint(String path, String method) {
        // Note: Since context-path is /api/v1,
        String[] publicPaths = {
            "/health",
            "/public",
            "/actuator",
            "/auth/login",
            "/auth/logout",
            "/auth/refresh",
            "/auth/forgot-password",
            "/auth/reset-password",
            "/auth/registration",
            "/registration",
            "/database",
            "/error"
        };

        log.debug("Checking if path '{}' is public. Available public paths: {}", path, String.join(", ", publicPaths));

        for (String publicPath : publicPaths) {
            if (path.startsWith(publicPath)) {
                log.debug("Path '{}' matches public path '{}'", path, publicPath);
                return true;
            }
        }

        log.debug("Path '{}' does not match any public paths", path);
        return false;
    }
    
    private void handleSessionValidationFailure(HttpServletResponse response, String action) 
            throws IOException {
        switch (action) {
            case "terminate":
                sendErrorResponse(response, 401, "session terminated due to security violation", null);
                break;
            case "challenge":
                sendErrorResponse(response, 200, "additional verification required", 
                    Map.of("requiresChallenge", true));
                break;
            default:
                sendErrorResponse(response, 401, "session validation failed", null);
        }
    }
    
    private void handleSuspiciousActivity(HttpServletResponse response, 
                                        SecurityService.SuspiciousActivityResult result) throws IOException {
        switch (result.getAction()) {
            case "block":
                sendErrorResponse(response, 403, "access blocked due to suspicious activity", 
                    Map.of("riskScore", result.getRiskScore(), "reasons", result.getReasons()));
                break;
            case "challenge":
                sendErrorResponse(response, 200, "additional verification required", 
                    Map.of("requiresChallenge", true, "riskScore", result.getRiskScore()));
                break;
            default:
                // allow but log
                log.warn("Suspicious activity detected but allowed - riskScore: {}, reasons: {}", 
                    result.getRiskScore(), result.getReasons());
        }
    }
    
    private void handleAuthenticationError(HttpServletRequest request, HttpServletResponse response, 
                                         Exception error) throws IOException {
        String ipAddress = getClientIpAddress(request);
        securityService.recordFailedAttempt(ipAddress, "ip");
        
        log.error("Authentication failed: {} - IP: {}", error.getMessage(), 
            securityService.maskSensitiveData(ipAddress));
        
        sendErrorResponse(response, 401, "invalid or expired token", null);
    }
    
    private void sendErrorResponse(HttpServletResponse response, int status, String message, 
                                 Map<String, Object> additionalData) throws IOException {
        response.setStatus(status);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("message", message);
        errorResponse.put("timestamp", System.currentTimeMillis());
        
        if (additionalData != null) {
            errorResponse.putAll(additionalData);
        }
        
        String jsonResponse = objectMapper.writeValueAsString(errorResponse);
        response.getWriter().write(jsonResponse);
    }
}
