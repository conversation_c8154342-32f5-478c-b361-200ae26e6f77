{"info": {"_postman_id": "aetrust-backend-api", "name": "AeTrust Backend API", "description": "Complete API collection for AeTrust fintech platform with multi-step registration and user management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Registration", "item": [{"name": "Step 1 - Initial Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+250788123456\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Doe\",\n  \"password\": \"6993954386\",\n  \"userType\": \"individual\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/registration/initiate", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "registration", "initiate"]}}}, {"name": "Step 2 - Verify Phone", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+250788123456\",\n  \"code\": \"123456\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/registration/verify-phone", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "registration", "verify-phone"]}}}, {"name": "Step 3 - <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"code\": \"123456\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/registration/verify-email", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "registration", "verify-email"]}}}, {"name": "Step 4 - Complete Personal Info", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+250788123456\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Doe\",\n  \"dateOfBirth\": \"1990-01-01\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/registration/personal-info", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "registration", "personal-info"]}}}, {"name": "Step 5 - Set Transaction PIN", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+250788123456\",\n  \"pin\": \"1234\",\n  \"confirmPin\": \"1234\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/registration/transaction-pin", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "registration", "transaction-pin"]}}}, {"name": "Step 6 - KYC Documents", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+250788123456\",\n  \"idType\": \"national_id\",\n  \"idNumber\": \"1234567890123456\",\n  \"idDocumentFront\": \"base64_encoded_image_front\",\n  \"idDocumentBack\": \"base64_encoded_image_back\",\n  \"selfiePhoto\": \"base64_encoded_selfie\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/registration/kyc-documents", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "registration", "kyc-documents"]}}}, {"name": "Step 7 - Biometric Setup", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+250788123456\",\n  \"biometricEnabled\": true,\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/registration/biometric-setup", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "registration", "biometric-setup"]}}}, {"name": "Step 8 - Agent Business Info", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+250788123456\",\n  \"businessName\": \"QuickCash Store\",\n  \"businessType\": \"sole_proprietorship\",\n  \"businessRegistrationNumber\": \"REG123456789\",\n  \"businessAddress\": \"123 Business Street, Kigali, Rwanda\",\n  \"businessDocument\": \"base64_encoded_document\",\n  \"taxCertificate\": \"base64_encoded_certificate\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/registration/agent-business-info", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "registration", "agent-business-info"]}}}, {"name": "Registration Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/auth/registration/status?phone=+250788123456", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "registration", "status"], "query": [{"key": "phone", "value": "+250788123456"}]}}}, {"name": "Registration Progress", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/registration/progress", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "registration", "progress"]}}}, {"name": "Resend Verification Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"+250788123456\",\n  \"codeType\": \"sms\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/registration/resend-verification", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "registration", "resend-verification"]}}}]}, {"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"6993954386\",\n  \"platform\": \"web\",\n  \"deviceInfo\": \"Chrome/Windows\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "login"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "logout"]}}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/refresh", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "refresh"]}}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/forgot-password", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "forgot-password"]}}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset_token_here\",\n  \"newPassword\": \"newPassword123\",\n  \"confirmPassword\": \"newPassword123\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/reset-password", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "reset-password"]}}}]}]}, {"name": "Users", "item": [{"name": "Profile Management", "item": [{"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/users/me", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "me"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"bio\": \"Software Developer\",\n  \"address\": \"123 Main St, Kigali, Rwanda\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/users/profile", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "profile"]}}}, {"name": "Upload Profile Picture", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}, {"key": "platform", "value": "web", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/v1/users/profile-picture", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "profile-picture"]}}}, {"name": "Change Password", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"oldPassword123\",\n  \"newPassword\": \"newPassword123\",\n  \"confirmPassword\": \"newPassword123\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/users/password", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "password"]}}}, {"name": "Delete Account", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"password\": \"currentPassword123\",\n  \"reason\": \"No longer needed\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/users/account", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "account"]}}}]}, {"name": "User Search & Discovery", "item": [{"name": "Search Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/users/search?query=john&limit=10&offset=0", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "search"], "query": [{"key": "query", "value": "john"}, {"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}]}}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "{{userId}}"]}}}]}, {"name": "Security Management", "item": [{"name": "Enable Two-Factor Authentication", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/users/security/2fa/enable", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "security", "2fa", "enable"]}}}, {"name": "Verify Two-Factor Setup", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"code\": \"123456\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/users/security/2fa/verify", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "security", "2fa", "verify"]}}}, {"name": "Change Transaction PIN", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPin\": \"1234\",\n  \"newPin\": \"5678\",\n  \"confirmPin\": \"5678\",\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/users/security/pin", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "security", "pin"]}}}, {"name": "Get Login History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/users/security/login-history?limit=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "security", "login-history"], "query": [{"key": "limit", "value": "10"}]}}}]}, {"name": "User Preferences", "item": [{"name": "Get Preferences", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/users/preferences", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "preferences"]}}}, {"name": "Update Preferences", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"language\": \"en\",\n  \"timezone\": \"Africa/Kigali\",\n  \"currency\": \"RWF\",\n  \"theme\": \"light\",\n  \"emailNotifications\": true,\n  \"smsNotifications\": true,\n  \"pushNotifications\": false,\n  \"platform\": \"web\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/users/preferences", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "preferences"]}}}]}, {"name": "User Analytics & Activity", "item": [{"name": "Get Activity History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/users/activity?limit=20&offset=0", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "activity"], "query": [{"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}]}}}]}, {"name": "Health Check", "item": [{"name": "Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/users/health", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "health"]}}}]}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000"}, {"key": "token", "value": ""}, {"key": "refreshToken", "value": ""}, {"key": "userId", "value": ""}]}