# AeTrust Backend API - Postman Collection

## Overview
This Postman collection contains all the API endpoints for the AeTrust fintech platform backend. The collection is organized into two main folders: **Auth** and **Users**, following the exact structure you requested.

## Collection Structure

### 📁 Auth Folder (`/api/v1/auth/`)
Contains all authentication and registration endpoints

#### 🔐 Registration Endpoints
The registration process follows an 8-step flow:

1. **Step 1 - Initial Registration** (`POST /registration/initiate`)
   - User provides email, phone, name, password, userType, and platform
   - Supports userType: `individual`, `business`, `agent`

2. **Step 2 - Verify Phone** (`POST /registration/verify-phone`)
   - Verify phone number with SMS code

3. **Step 3 - Verify Email** (`POST /registration/verify-email`)
   - Verify email address with email code

4. **Step 4 - Complete Personal Info** (`POST /registration/personal-info`)
   - Complete personal information including date of birth

5. **Step 5 - Set Transaction PIN** (`POST /registration/transaction-pin`)
   - Set 4-digit transaction PIN with confirmation

6. **Step 6 - KYC Documents** (`POST /registration/kyc-documents`)
   - Upload identity documents (front, back, selfie)
   - Supports ID types: `national_id`, `passport`, `drivers_license`, `voters_card`

7. **Step 7 - Biometric Setup** (`POST /registration/biometric-setup`)
   - Enable/disable biometric authentication

8. **Step 8 - Agent Business Info** (`POST /registration/agent-business-info`)
   - For agent users only - business registration details
   - Includes business documents and tax certificates

#### Additional Registration Endpoints:
- **Registration Status** (`GET /registration/status`) - Check registration progress
- **Registration Progress** (`GET /registration/progress`) - Detailed progress info
- **Resend Verification** (`POST /registration/resend-verification`) - Resend SMS/email codes

#### 🔑 Authentication Endpoints
- **Login** (`POST /auth/login`) - User authentication
- **Logout** (`POST /auth/logout`) - End user session
- **Refresh Token** (`POST /auth/refresh`) - Refresh access token
- **Forgot Password** (`POST /auth/forgot-password`) - Password reset request
- **Reset Password** (`POST /auth/reset-password`) - Complete password reset

### 📁 Users Folder (`/api/v1/users/`)
Contains all user management endpoints

#### 👤 Profile Management
- **Get Current User** (`GET /users/me`) - Get authenticated user profile
- **Update Profile** (`PUT /users/profile`) - Update user profile information
- **Upload Profile Picture** (`POST /users/profile-picture`) - Upload profile image
- **Change Password** (`PUT /users/password`) - Change user password
- **Delete Account** (`DELETE /users/account`) - Delete user account

#### 🔍 User Search & Discovery
- **Search Users** (`GET /users/search`) - Search for users by query
- **Get User by ID** (`GET /users/{userId}`) - Get specific user details

#### 🔐 Security Management
- **Enable Two-Factor Authentication** (`POST /users/security/2fa/enable`)
- **Verify Two-Factor Setup** (`POST /users/security/2fa/verify`)
- **Change Transaction PIN** (`PUT /users/security/pin`)
- **Get Login History** (`GET /users/security/login-history`)

#### ⚙️ User Preferences
- **Get Preferences** (`GET /users/preferences`) - Get user preferences
- **Update Preferences** (`PUT /users/preferences`) - Update user preferences

#### 📊 User Analytics & Activity
- **Get Activity History** (`GET /users/activity`) - Get user activity log

#### 🏥 Health Check
- **Service Health** (`GET /users/health`) - Check service status

## Environment Variables

The collection uses the following environment variables:

```json
{
  "baseUrl": "http://localhost:3000",
  "token": "",
  "refreshToken": "",
  "userId": ""
}
```

### Setting Up Environment Variables:

1. **baseUrl**: Set to your backend server URL (default: `http://localhost:3000`)
2. **token**: Will be automatically set after successful login
3. **refreshToken**: Will be automatically set after successful login
4. **userId**: Will be automatically set after successful login

## How to Use

### 1. Import the Collection
1. Open Postman
2. Click "Import" button
3. Select the `AeTrust_Backend_API.postman_collection.json` file
4. The collection will be imported with all endpoints

### 2. Set Environment Variables
1. Create a new environment in Postman
2. Add the variables listed above
3. Set the `baseUrl` to your backend server URL

### 3. Test Registration Flow
1. Start with "Step 1 - Initial Registration"
2. Follow the steps in order (1-8)
3. Use the phone number from Step 1 in subsequent steps
4. For agent users, complete Step 8 (Agent Business Info)

### 4. Test Authentication
1. Use the Login endpoint with registered credentials
2. Copy the returned token to the `token` environment variable
3. Use authenticated endpoints with the Bearer token

## Sample Data

### Individual User Registration:
```json
{
  "email": "<EMAIL>",
  "phone": "+250788123456",
  "firstName": "John",
  "lastName": "Doe",
  "password": "6993954386",
  "userType": "individual",
  "platform": "web"
}
```

### Agent User Registration:
```json
{
  "email": "<EMAIL>",
  "phone": "+250788123457",
  "firstName": "Jane",
  "lastName": "Smith",
  "password": "SecurePass123!",
  "userType": "agent",
  "platform": "web"
}
```

### Business User Registration:
```json
{
  "email": "<EMAIL>",
  "phone": "+250788123458",
  "firstName": "Business",
  "lastName": "Owner",
  "password": "BusinessPass123!",
  "userType": "business",
  "platform": "web"
}
```

## Response Format

All endpoints return responses in this format:

### Success Response:
```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    // Response data here
  },
  "timestamp": "2025-09-03T18:00:00Z"
}
```

### Error Response:
```json
{
  "success": false,
  "message": "Error description",
  "error": "ERROR_CODE",
  "timestamp": "2025-09-03T18:00:00Z"
}
```

## Testing Tips

1. **Sequential Testing**: Follow the registration steps in order
2. **Phone Number Consistency**: Use the same phone number throughout the registration flow
3. **Token Management**: Save tokens from login responses for authenticated endpoints
4. **Error Handling**: Check error responses for validation issues
5. **File Uploads**: For document uploads, use base64 encoded strings in the JSON body

## Troubleshooting

### Common Issues:
1. **401 Unauthorized**: Check if token is set correctly in Authorization header
2. **400 Bad Request**: Verify request body format and required fields
3. **404 Not Found**: Ensure the endpoint URL is correct
4. **500 Internal Server Error**: Check server logs for backend issues

### Support:
- Check the backend logs for detailed error information
- Verify database connectivity
- Ensure all required environment variables are set in the backend

## Notes

- All endpoints use JSON format for request/response bodies
- File uploads for documents should be base64 encoded
- Phone numbers must be in international format (e.g., +250788123456)
- Passwords must meet security requirements (8+ chars, uppercase, lowercase, number, special char)
- Transaction PINs must be exactly 4 digits
