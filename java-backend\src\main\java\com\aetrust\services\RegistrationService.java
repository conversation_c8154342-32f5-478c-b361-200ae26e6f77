package com.aetrust.services;

import com.aetrust.dto.RequestDTOs.*;
import com.aetrust.models.User;
import com.aetrust.types.Types.*;
import com.aetrust.utils.CryptoUtils;
import com.aetrust.utils.JwtUtils;
import com.aetrust.utils.RetryUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.aetrust.repositories.UserRepository;
import com.aetrust.repositories.UserProfileRepository;
import com.aetrust.repositories.UserSecurityRepository;
// import org.springframework.data.redis.core.RedisTemplate; // Redis disabled
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class RegistrationService {
    
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private UserSecurityRepository userSecurityRepository;

    // Global cache service - auto-switches between Redis and in-memory
    @Autowired
    private CacheService cacheService;
    
    @Autowired
    private CryptoUtils cryptoUtils;
    
    @Autowired
    private JwtUtils jwtUtils;
    
    @Autowired
    private NotificationService notificationService;

    @Autowired
    private ObjectMapper objectMapper;
    
    public RegistrationResult initiateRegistration(RegistrationInitRequest request) {
        try {
            if (userRepository.existsByEmailAndDeletedAtIsNull(request.getEmail())) {
                return RegistrationResult.error("email already registered", "EMAIL_EXISTS");
            }

            if (userRepository.existsByPhoneAndDeletedAtIsNull(request.getPhone())) {
                return RegistrationResult.error("phone number already registered", "PHONE_EXISTS");
            }
            
            String registrationId = cryptoUtils.generateSecureToken(32);
            RegistrationSession session = new RegistrationSession();
            session.setRegistrationId(registrationId);
            session.setEmail(request.getEmail());
            session.setPhone(request.getPhone());
            session.setFirstName(request.getFirstName());
            session.setLastName(request.getLastName());
            session.setPasswordHash(cryptoUtils.hashPassword(request.getPassword()));
            session.setUserType(request.getUserType());
            session.setPlatform(request.getPlatform());
            session.setDateOfBirth(request.getDateOfBirth());
            session.setCurrentStep(RegistrationStep.PHONE_VERIFICATION);
            session.setCreatedAt(System.currentTimeMillis());
            session.setExpiresAt(System.currentTimeMillis() + 900000); // 15 minutes
            

            String sessionKey = "registration:" + registrationId;
            String sessionJson = objectMapper.writeValueAsString(session);
            cacheService.set(sessionKey, sessionJson, 15, TimeUnit.MINUTES);

            String verificationCode = cryptoUtils.generateOTP(6);
            String codeKey = "verification:phone:" + request.getPhone();
            cacheService.set(codeKey, verificationCode, 5, TimeUnit.MINUTES);
            
            notificationService.sendSMS(request.getPhone(), 
                "Your AeTrust verification code is: " + verificationCode);
            
            log.info("Registration initiated for phone: {}",
                cryptoUtils.maskSensitiveData(request.getPhone(), 3));
            
            return RegistrationResult.success("registration initiated", registrationId, 
                RegistrationStep.PHONE_VERIFICATION.getValue());
                
        } catch (Exception error) {
            log.error("Error initiating registration: {}", error.getMessage());
            return RegistrationResult.error("registration initiation failed", "INTERNAL_ERROR");
        }
    }
    
    public VerificationResult verifyPhone(PhoneVerificationRequest request) {
        try {

            String codeKey = "verification:phone:" + request.getPhone();
            String storedCode = cacheService.get(codeKey);
            
            if (storedCode == null) {
                return VerificationResult.error("verification code expired", "CODE_EXPIRED");
            }
            
            if (!cryptoUtils.constantTimeCompare(request.getCode(), storedCode)) {
                return VerificationResult.error("invalid verification code", "INVALID_CODE");
            }
            
            RegistrationSession session = findRegistrationSession(request.getPhone());
            if (session == null) {
                return VerificationResult.error("registration session not found", "SESSION_NOT_FOUND");
            }
            
            session.setPhoneVerified(true);
            session.setCurrentStep(RegistrationStep.EMAIL_VERIFICATION);
            updateRegistrationSession(session);
            
            cacheService.delete(codeKey);

            String emailCode = cryptoUtils.generateOTP(6);
            String emailCodeKey = "verification:email:" + session.getEmail();
            cacheService.set(emailCodeKey, emailCode, 5, TimeUnit.MINUTES);
            
            notificationService.sendEmail(session.getEmail(), 
                "AeTrust Email Verification", 
                "Your verification code is: " + emailCode);
            
            return VerificationResult.success("phone verified", session.getRegistrationId(), 
                RegistrationStep.EMAIL_VERIFICATION.getValue());
                
        } catch (Exception error) {
            log.error("Error verifying phone: {}", error.getMessage());
            return VerificationResult.error("phone verification failed", "INTERNAL_ERROR");
        }
    }
    
    public VerificationResult verifyEmail(EmailVerificationRequest request) {
        try {

            String codeKey = "verification:email:" + request.getEmail();
            String storedCode = cacheService.get(codeKey);
            
            if (storedCode == null) {
                return VerificationResult.error("verification code expired", "CODE_EXPIRED");
            }
            
            if (!cryptoUtils.constantTimeCompare(request.getCode(), storedCode)) {
                return VerificationResult.error("invalid verification code", "INVALID_CODE");
            }
            

            RegistrationSession session = findRegistrationSessionByEmail(request.getEmail());
            if (session == null) {
                return VerificationResult.error("registration session not found", "SESSION_NOT_FOUND");
            }
            
            session.setEmailVerified(true);
            session.setCurrentStep(RegistrationStep.TRANSACTION_PIN);
            updateRegistrationSession(session);
            
            cacheService.delete(codeKey);
            
            return VerificationResult.success("email verified", session.getRegistrationId(), 
                RegistrationStep.TRANSACTION_PIN.getValue());
                
        } catch (Exception error) {
            log.error("Error verifying email: {}", error.getMessage());
            return VerificationResult.error("email verification failed", "INTERNAL_ERROR");
        }
    }
    
    public CompletionResult completeRegistration(RegistrationCompleteRequest request) {
        try {

            RegistrationSession session = findRegistrationSession(request.getPhone());
            if (session == null) {
                return CompletionResult.error("registration session not found", "SESSION_NOT_FOUND");
            }
            
            if (!session.isPhoneVerified() || !session.isEmailVerified()) {
                return CompletionResult.error("verification not completed", "VERIFICATION_INCOMPLETE");
            }
            
            User user = new User();
            user.setEmail(session.getEmail());
            user.setPhone(session.getPhone());
            user.setPassword(session.getPasswordHash());
            user.setRole(UserRole.USER);
            user.setAccountStatus(AccountStatus.ACTIVE);
            user.setRegistrationStep(RegistrationStep.COMPLETED);
            user.setPhoneVerified(true);
            user.setEmailVerified(true);
            user.setRegistrationCompleted(true);

            User savedUser = RetryUtils.withDatabaseRetry(() -> {
                return userRepository.save(user);
            }, "user-registration-save");

            User.UserProfile profile = new User.UserProfile();
            profile.setUserId(savedUser.getId());
            profile.setUserUuid(savedUser.getUserUuid());
            profile.setFirstName(session.getFirstName());
            profile.setLastName(session.getLastName());
            profile.setDateOfBirth(session.getDateOfBirth());
            userProfileRepository.save(profile);

            User.UserSecurity security = new User.UserSecurity();
            security.setUserId(savedUser.getId());
            security.setUserUuid(savedUser.getUserUuid());
            security.setTransactionPinSet(true);
            String pinHash = cryptoUtils.hashPassword(request.getPin());
            security.setTransactionPinHash(pinHash);
            userSecurityRepository.save(security);

            String accessToken = jwtUtils.generateToken(
                savedUser.getId().toString(), savedUser.getEmail(), savedUser.getRole(),
                savedUser.isVerified(), savedUser.getKycStatus());
            String refreshToken = jwtUtils.generateRefreshToken(savedUser.getId().toString());
            
            Map<String, Object> userProfile = createUserProfile(savedUser);
            
            cleanupRegistrationSession(session.getRegistrationId());
            
            log.info("Registration completed for user: {}",
                cryptoUtils.maskSensitiveData(savedUser.getEmail(), 2));
            
            return CompletionResult.success("registration completed", accessToken, 
                refreshToken, userProfile, true);
                
        } catch (Exception error) {
            log.error("Error completing registration: {}", error.getMessage());
            return CompletionResult.error("registration completion failed", "INTERNAL_ERROR");
        }
    }
    
    public StatusResult getRegistrationStatus(String phone) {
        try {
            RegistrationSession session = findRegistrationSession(phone);
            if (session == null) {
                return StatusResult.notFound();
            }
            
            return StatusResult.found(session.getCurrentStep().getValue(), 
                session.isPhoneVerified(), session.isEmailVerified(), 
                session.getCurrentStep() == RegistrationStep.COMPLETED, 
                session.getExpiresAt());
                
        } catch (Exception error) {
            log.error("Error getting registration status: {}", error.getMessage());
            return StatusResult.notFound();
        }
    }
    
    public ResendResult resendVerificationCode(ResendCodeRequest request) {
        try {
            RegistrationSession session = findRegistrationSession(request.getPhone());
            if (session == null) {
                return ResendResult.error("registration session not found", "SESSION_NOT_FOUND");
            }
            
            String verificationCode = cryptoUtils.generateOTP(6);
            
            if ("sms".equals(request.getCodeType())) {
                String codeKey = "verification:phone:" + request.getPhone();
                cacheService.set(codeKey, verificationCode, 5, TimeUnit.MINUTES);

                notificationService.sendSMS(request.getPhone(),
                    "Your AeTrust verification code is: " + verificationCode);

                return ResendResult.success("SMS code sent", "sms");

            } else if ("email".equals(request.getCodeType())) {
                String codeKey = "verification:email:" + session.getEmail();
                cacheService.set(codeKey, verificationCode, 5, TimeUnit.MINUTES);
                
                notificationService.sendEmail(session.getEmail(), 
                    "AeTrust Email Verification", 
                    "Your verification code is: " + verificationCode);
                    
                return ResendResult.success("Email code sent", "email");
            }
            
            return ResendResult.error("invalid code type", "INVALID_CODE_TYPE");
            
        } catch (Exception error) {
            log.error("Error resending verification code: {}", error.getMessage());
            return ResendResult.error("failed to resend code", "INTERNAL_ERROR");
        }
    }


    private RegistrationSession findRegistrationSession(String phone) {
        try {

            String pattern = "registration:*";
            for (String key : cacheService.keys(pattern)) {
                String sessionData = cacheService.get(key);
                if (sessionData != null) {
                    RegistrationSession session = objectMapper.readValue(sessionData, RegistrationSession.class);
                    if (phone.equals(session.getPhone())) {
                        return session;
                    }
                }
            }
            return null;
        } catch (Exception e) {
            log.error("Error finding registration session: {}", e.getMessage());
            return null;
        }
    }

    private RegistrationSession findRegistrationSessionByEmail(String email) {
        try {
            String pattern = "registration:*";
            for (String key : cacheService.keys(pattern)) {
                String sessionData = cacheService.get(key);
                if (sessionData != null) {
                    RegistrationSession session = objectMapper.readValue(sessionData, RegistrationSession.class);
                    if (email.equals(session.getEmail())) {
                        return session;
                    }
                }
            }
            return null;
        } catch (Exception e) {
            log.error("Error finding registration session by email: {}", e.getMessage());
            return null;
        }
    }

    private void updateRegistrationSession(RegistrationSession session) {
        try {
            String sessionKey = "registration:" + session.getRegistrationId();
            String sessionJson = objectMapper.writeValueAsString(session);
            cacheService.set(sessionKey, sessionJson, 15, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("Error updating registration session: {}", e.getMessage());
        }
    }

    private void cleanupRegistrationSession(String registrationId) {
        try {
            String sessionKey = "registration:" + registrationId;
            cacheService.delete(sessionKey);
        } catch (Exception e) {
            log.error("Error cleaning up registration session: {}", e.getMessage());
        }
    }

    private Map<String, Object> createUserProfile(User user) {
        Map<String, Object> profile = new HashMap<>();
        profile.put("userId", user.getId());
        profile.put("email", user.getEmail());
        profile.put("phoneNumber", user.getPhone());

        User.UserProfile userProfile = userProfileRepository.findByUserIdAndDeletedAtIsNull(user.getId()).orElse(null);
        if (userProfile != null) {
            profile.put("firstName", userProfile.getFirstName());
            profile.put("lastName", userProfile.getLastName());
            profile.put("fullName", userProfile.getFullName());
        } else {
            profile.put("firstName", null);
            profile.put("lastName", null);
            profile.put("fullName", null);
        }

        profile.put("userRole", user.getRole().name());
        profile.put("isVerified", user.isVerified());
        profile.put("kycStatus", user.getKycStatus().name());
        profile.put("walletBalance", "0.00"); 
        profile.put("accountCreated", user.getCreatedAt());
        return profile;
    }



    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RegistrationSession {
        private String registrationId;
        private String email;
        private String phone;
        private String firstName;
        private String lastName;
        private String passwordHash;
        private String userType;
        private String platform;
        private java.time.LocalDate dateOfBirth;
        private RegistrationStep currentStep;
        private boolean phoneVerified = false;
        private boolean emailVerified = false;
        private long createdAt;
        private long expiresAt;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RegistrationResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String registrationId;
        private String nextStep;

        public static RegistrationResult success(String message, String registrationId, String nextStep) {
            return new RegistrationResult(true, message, null, registrationId, nextStep);
        }

        public static RegistrationResult error(String message, String errorCode) {
            return new RegistrationResult(false, message, errorCode, null, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VerificationResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String registrationId;
        private String nextStep;
        private boolean completed = false;
        private String accessToken;
        private String refreshToken;
        private Map<String, Object> userProfile;

        public static VerificationResult success(String message, String registrationId, String nextStep) {
            return new VerificationResult(true, message, null, registrationId, nextStep, false, null, null, null);
        }

        public static VerificationResult error(String message, String errorCode) {
            return new VerificationResult(false, message, errorCode, null, null, false, null, null, null);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompletionResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String accessToken;
        private String refreshToken;
        private Map<String, Object> userProfile;
        private boolean walletCreated;

        public static CompletionResult success(String message, String accessToken, String refreshToken,
                                            Map<String, Object> userProfile, boolean walletCreated) {
            return new CompletionResult(true, message, null, accessToken, refreshToken, userProfile, walletCreated);
        }

        public static CompletionResult error(String message, String errorCode) {
            return new CompletionResult(false, message, errorCode, null, null, null, false);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusResult {
        private boolean found;
        private String currentStep;
        private boolean phoneVerified;
        private boolean emailVerified;
        private boolean completed;
        private long expiresAt;

        public static StatusResult found(String currentStep, boolean phoneVerified, boolean emailVerified,
                                       boolean completed, long expiresAt) {
            return new StatusResult(true, currentStep, phoneVerified, emailVerified, completed, expiresAt);
        }

        public static StatusResult notFound() {
            return new StatusResult(false, null, false, false, false, 0);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResendResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String codeType;

        public static ResendResult success(String message, String codeType) {
            return new ResendResult(true, message, null, codeType);
        }

        public static ResendResult error(String message, String errorCode) {
            return new ResendResult(false, message, errorCode, null);
        }

        public String getVerificationType() {
            return this.codeType;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PersonalInfoResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String registrationId;
        private String nextStep;

        public static PersonalInfoResult success(String message, String registrationId, String nextStep) {
            return new PersonalInfoResult(true, message, null, registrationId, nextStep);
        }

        public static PersonalInfoResult error(String message, String errorCode) {
            return new PersonalInfoResult(false, message, errorCode, null, null);
        }

        public String getProfileCompleteness() {
            return success ? "75%" : "50%";
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IdentityVerificationResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String registrationId;
        private String verificationStatus;

        public static IdentityVerificationResult success(String message, String registrationId, String status) {
            return new IdentityVerificationResult(true, message, null, registrationId, status);
        }

        public static IdentityVerificationResult error(String message, String errorCode) {
            return new IdentityVerificationResult(false, message, errorCode, null, null);
        }

        public String getNextStep() {
            return success ? "transaction_pin" : "identity_verification";
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransactionPinResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String registrationId;
        private String nextStep;

        public static TransactionPinResult success(String message, String registrationId, String nextStep) {
            return new TransactionPinResult(true, message, null, registrationId, nextStep);
        }

        public static TransactionPinResult error(String message, String errorCode) {
            return new TransactionPinResult(false, message, errorCode, null, null);
        }

        public String getSecurityLevel() {
            return success ? "enhanced" : "basic";
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BiometricResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String registrationId;
        private String enrollmentStatus;

        public static BiometricResult success(String message, String registrationId, String status) {
            return new BiometricResult(true, message, null, registrationId, status);
        }

        public static BiometricResult error(String message, String errorCode) {
            return new BiometricResult(false, message, errorCode, null, null);
        }

        public String getEnrollmentType() {
            return success ? "fingerprint" : "none";
        }

        public String getSecurityLevel() {
            return success ? "high" : "medium";
        }

        public String getNextStep() {
            return success ? "completion" : "biometric_enrollment";
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessVerificationResult {
        private boolean success;
        private String message;
        private String errorCode;
        private String registrationId;
        private String verificationStatus;

        public static BusinessVerificationResult success(String message, String registrationId, String status) {
            return new BusinessVerificationResult(true, message, null, registrationId, status);
        }

        public static BusinessVerificationResult error(String message, String errorCode) {
            return new BusinessVerificationResult(false, message, errorCode, null, null);
        }

        public String getBusinessType() {
            return success ? "verified_business" : "unverified";
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProgressResult {
        private boolean success;
        private String message;
        private String errorCode;
        private Map<String, Object> progress;

        public static ProgressResult success(String message, Map<String, Object> progress) {
            return new ProgressResult(true, message, null, progress);
        }

        public static ProgressResult error(String message, String errorCode) {
            return new ProgressResult(false, message, errorCode, null);
        }

        public String getCurrentStep() {
            return progress != null ? (String) progress.get("currentStep") : "unknown";
        }

        public int getCompletedSteps() {
            if (progress == null) return 0;
            int completed = 0;
            if (Boolean.TRUE.equals(progress.get("phoneVerified"))) completed++;
            if (Boolean.TRUE.equals(progress.get("emailVerified"))) completed++;
            if (Boolean.TRUE.equals(progress.get("personalInfoCompleted"))) completed++;
            if (Boolean.TRUE.equals(progress.get("identityVerificationSubmitted"))) completed++;
            if (Boolean.TRUE.equals(progress.get("transactionPinSet"))) completed++;
            if (Boolean.TRUE.equals(progress.get("biometricEnrolled"))) completed++;
            if (Boolean.TRUE.equals(progress.get("businessVerificationSubmitted"))) completed++;
            return completed;
        }

        public int getTotalSteps() {
            return 7; // phone, email, personal, identity, pin, biometric, business
        }

        public double getProgressPercentage() {
            return (double) getCompletedSteps() / getTotalSteps() * 100;
        }

        public String[] getNextSteps() {
            if (progress == null) return new String[]{"phone_verification"};
            String currentStep = getCurrentStep();
            switch (currentStep) {
                case "phone_verification": return new String[]{"email_verification"};
                case "email_verification": return new String[]{"personal_info"};
                case "personal_info": return new String[]{"identity_verification"};
                case "identity_verification": return new String[]{"transaction_pin"};
                case "transaction_pin": return new String[]{"biometric_enrollment"};
                case "biometric_enrollment": return new String[]{"business_verification"};
                case "business_verification": return new String[]{"completion"};
                default: return new String[]{"completion"};
            }
        }

        public boolean isCompleted() {
            return progress != null && Boolean.TRUE.equals(progress.get("completed"));
        }
    }

    public PersonalInfoResult completePersonalInfo(String registrationId, CompletePersonalInfoRequest request) {
        try {
            if (registrationId == null || registrationId.trim().isEmpty()) {
                return PersonalInfoResult.error("invalid registration id", "INVALID_REGISTRATION_ID");
            }

            String cacheKey = "registration:" + registrationId;
            String cachedData = cacheService.get(cacheKey);
            Map<String, Object> regData = null;
            if (cachedData != null) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    regData = mapper.readValue(cachedData, Map.class);
                } catch (Exception e) {
                    log.error("Error parsing cached registration data: {}", e.getMessage());
                }
            }

            if (regData == null) {
                return PersonalInfoResult.error("registration session not found", "SESSION_NOT_FOUND");
            }

            regData.put("firstName", request.getFirstName());
            regData.put("lastName", request.getLastName());
            regData.put("dateOfBirth", request.getDateOfBirth());
            regData.put("gender", request.getGender());
            regData.put("address", request.getAddress());
            regData.put("personalInfoCompleted", true);
            regData.put("currentStep", "identity_verification");

            try {
                ObjectMapper mapper = new ObjectMapper();
                String jsonData = mapper.writeValueAsString(regData);
                cacheService.set(cacheKey, jsonData, 30, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("Error saving registration data to cache: {}", e.getMessage());
                return PersonalInfoResult.error("failed to save registration data", "CACHE_ERROR");
            }

            return PersonalInfoResult.success("personal information saved successfully", registrationId, "identity_verification");

        } catch (Exception error) {
            log.error("Error completing personal info: {}", error.getMessage());
            return PersonalInfoResult.error("failed to save personal information", "INTERNAL_ERROR");
        }
    }

    public IdentityVerificationResult submitIdentityVerification(String registrationId, SubmitIdentityRequest request) {
        try {
            if (registrationId == null || registrationId.trim().isEmpty()) {
                return IdentityVerificationResult.error("invalid registration id", "INVALID_REGISTRATION_ID");
            }

            String cacheKey = "registration:" + registrationId;
            String cachedData = cacheService.get(cacheKey);
            Map<String, Object> regData = null;
            if (cachedData != null) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    regData = mapper.readValue(cachedData, Map.class);
                } catch (Exception e) {
                    log.error("Error parsing cached registration data: {}", e.getMessage());
                }
            }

            if (regData == null) {
                return IdentityVerificationResult.error("registration session not found", "SESSION_NOT_FOUND");
            }

            regData.put("identityType", request.getDocumentType());
            regData.put("identityNumber", request.getDocumentNumber());
            regData.put("frontImageUrl", request.getFrontImage());
            regData.put("backImageUrl", request.getBackImage());
            regData.put("identityVerificationSubmitted", true);
            regData.put("identityVerificationStatus", "pending");
            regData.put("currentStep", "transaction_pin");

            try {
                ObjectMapper mapper = new ObjectMapper();
                String jsonData = mapper.writeValueAsString(regData);
                cacheService.set(cacheKey, jsonData, 30, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("Error saving registration data to cache: {}", e.getMessage());
                return IdentityVerificationResult.error("failed to save registration data", "CACHE_ERROR");
            }

            return IdentityVerificationResult.success("identity verification submitted successfully", registrationId, "pending");

        } catch (Exception error) {
            log.error("Error submitting identity verification: {}", error.getMessage());
            return IdentityVerificationResult.error("failed to submit identity verification", "INTERNAL_ERROR");
        }
    }

    public TransactionPinResult setTransactionPin(String registrationId, SetTransactionPinRequest request) {
        try {
            if (registrationId == null || registrationId.trim().isEmpty()) {
                return TransactionPinResult.error("invalid registration id", "INVALID_REGISTRATION_ID");
            }

            String cacheKey = "registration:" + registrationId;
            String cachedData = cacheService.get(cacheKey);
            Map<String, Object> regData = null;
            if (cachedData != null) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    regData = mapper.readValue(cachedData, Map.class);
                } catch (Exception e) {
                    log.error("Error parsing cached registration data: {}", e.getMessage());
                }
            }

            if (regData == null) {
                return TransactionPinResult.error("registration session not found", "SESSION_NOT_FOUND");
            }

            String hashedPin = cryptoUtils.hashPassword(request.getPin());
            regData.put("transactionPin", hashedPin);
            regData.put("transactionPinSet", true);
            regData.put("currentStep", "biometric_enrollment");

            try {
                ObjectMapper mapper = new ObjectMapper();
                String jsonData = mapper.writeValueAsString(regData);
                cacheService.set(cacheKey, jsonData, 30, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("Error saving registration data to cache: {}", e.getMessage());
                return TransactionPinResult.error("failed to save registration data", "CACHE_ERROR");
            }

            return TransactionPinResult.success("transaction pin set successfully", registrationId, "biometric_enrollment");

        } catch (Exception error) {
            log.error("Error setting transaction pin: {}", error.getMessage());
            return TransactionPinResult.error("failed to set transaction pin", "INTERNAL_ERROR");
        }
    }

    public BiometricResult setBiometricEnrollment(String registrationId, BiometricEnrollmentRequest request) {
        try {
            if (registrationId == null || registrationId.trim().isEmpty()) {
                return BiometricResult.error("invalid registration id", "INVALID_REGISTRATION_ID");
            }

            String cacheKey = "registration:" + registrationId;
            String cachedData = cacheService.get(cacheKey);
            Map<String, Object> regData = null;
            if (cachedData != null) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    regData = mapper.readValue(cachedData, Map.class);
                } catch (Exception e) {
                    log.error("Error parsing cached registration data: {}", e.getMessage());
                }
            }

            if (regData == null) {
                return BiometricResult.error("registration session not found", "SESSION_NOT_FOUND");
            }

            regData.put("biometricType", request.getBiometricType());
            regData.put("biometricData", request.getBiometricData());
            regData.put("biometricEnrolled", true);

            String userType = (String) regData.get("userType");
            String nextStep = "BUSINESS".equals(userType) ? "business_verification" : "completion";
            regData.put("currentStep", nextStep);

            try {
                ObjectMapper mapper = new ObjectMapper();
                String jsonData = mapper.writeValueAsString(regData);
                cacheService.set(cacheKey, jsonData, 30, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("Error saving registration data to cache: {}", e.getMessage());
                return BiometricResult.error("failed to save registration data", "CACHE_ERROR");
            }

            return BiometricResult.success("biometric enrollment completed successfully", registrationId, "enrolled");

        } catch (Exception error) {
            log.error("Error setting biometric enrollment: {}", error.getMessage());
            return BiometricResult.error("failed to enroll biometric", "INTERNAL_ERROR");
        }
    }

    public BusinessVerificationResult submitBusinessVerification(String registrationId, BusinessVerificationRequest request) {
        try {
            if (registrationId == null || registrationId.trim().isEmpty()) {
                return BusinessVerificationResult.error("invalid registration id", "INVALID_REGISTRATION_ID");
            }

            String cacheKey = "registration:" + registrationId;
            String cachedData = cacheService.get(cacheKey);
            Map<String, Object> regData = null;
            if (cachedData != null) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    regData = mapper.readValue(cachedData, Map.class);
                } catch (Exception e) {
                    log.error("Error parsing cached registration data: {}", e.getMessage());
                }
            }

            if (regData == null) {
                return BusinessVerificationResult.error("registration session not found", "SESSION_NOT_FOUND");
            }

            regData.put("businessName", request.getBusinessName());
            regData.put("businessType", request.getBusinessType());
            regData.put("businessRegistrationNumber", request.getRegistrationNumber());
            regData.put("businessAddress", request.getBusinessAddress());
            regData.put("businessDocumentUrl", request.getBusinessCertificate());
            regData.put("businessVerificationSubmitted", true);
            regData.put("businessVerificationStatus", "pending");
            regData.put("currentStep", "completion");

            try {
                ObjectMapper mapper = new ObjectMapper();
                String jsonData = mapper.writeValueAsString(regData);
                cacheService.set(cacheKey, jsonData, 30, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("Error saving registration data to cache: {}", e.getMessage());
                return BusinessVerificationResult.error("failed to save registration data", "CACHE_ERROR");
            }

            return BusinessVerificationResult.success("business verification submitted successfully", registrationId, "pending");

        } catch (Exception error) {
            log.error("Error submitting business verification: {}", error.getMessage());
            return BusinessVerificationResult.error("failed to submit business verification", "INTERNAL_ERROR");
        }
    }

    public ProgressResult getRegistrationProgress(String registrationId) {
        try {
            if (registrationId == null || registrationId.trim().isEmpty()) {
                return ProgressResult.error("invalid registration id", "INVALID_REGISTRATION_ID");
            }

            String cacheKey = "registration:" + registrationId;
            String cachedData = cacheService.get(cacheKey);
            Map<String, Object> regData = null;
            if (cachedData != null) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    regData = mapper.readValue(cachedData, Map.class);
                } catch (Exception e) {
                    log.error("Error parsing cached registration data: {}", e.getMessage());
                }
            }

            if (regData == null) {
                return ProgressResult.error("registration session not found", "SESSION_NOT_FOUND");
            }

            Map<String, Object> progress = new HashMap<>();
            progress.put("registrationId", registrationId);
            progress.put("currentStep", regData.get("currentStep"));
            progress.put("phoneVerified", regData.getOrDefault("phoneVerified", false));
            progress.put("emailVerified", regData.getOrDefault("emailVerified", false));
            progress.put("personalInfoCompleted", regData.getOrDefault("personalInfoCompleted", false));
            progress.put("identityVerificationSubmitted", regData.getOrDefault("identityVerificationSubmitted", false));
            progress.put("transactionPinSet", regData.getOrDefault("transactionPinSet", false));
            progress.put("biometricEnrolled", regData.getOrDefault("biometricEnrolled", false));
            progress.put("businessVerificationSubmitted", regData.getOrDefault("businessVerificationSubmitted", false));
            progress.put("completed", regData.getOrDefault("completed", false));

            return ProgressResult.success("registration progress retrieved successfully", progress);

        } catch (Exception error) {
            log.error("Error getting registration progress: {}", error.getMessage());
            return ProgressResult.error("failed to get registration progress", "INTERNAL_ERROR");
        }
    }

    public ResendResult resendVerification(ResendVerificationRequest request) {
        try {
            String phone = request.getPhone();
            String verificationType = request.getVerificationType();

            if (phone == null || phone.trim().isEmpty()) {
                return ResendResult.error("invalid phone number", "INVALID_PHONE");
            }

            // find registration by phone number
            String cacheKey = "registration_phone:" + phone;
            String cachedData = cacheService.get(cacheKey);
            Map<String, Object> regData = null;
            if (cachedData != null) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    regData = mapper.readValue(cachedData, Map.class);
                } catch (Exception e) {
                    log.error("Error parsing cached registration data: {}", e.getMessage());
                }
            }

            if (regData == null) {
                return ResendResult.error("registration session not found", "SESSION_NOT_FOUND");
            }

            String verificationCode = generateVerificationCode();
            String contact = "";

            if ("sms".equals(verificationType)) {
                contact = phone;
                regData.put("phoneVerificationCode", verificationCode);
                notificationService.sendSMS(contact, "Your AeTrust verification code is: " + verificationCode);
            } else if ("email".equals(verificationType)) {
                contact = (String) regData.get("email");
                regData.put("emailVerificationCode", verificationCode);
                notificationService.sendEmail(contact, "AeTrust Verification Code",
                    "Your verification code is: " + verificationCode);
            } else {
                return ResendResult.error("invalid verification type", "INVALID_VERIFICATION_TYPE");
            }

            try {
                ObjectMapper mapper = new ObjectMapper();
                String jsonData = mapper.writeValueAsString(regData);
                cacheService.set(cacheKey, jsonData, 30, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("Error saving registration data to cache: {}", e.getMessage());
                return ResendResult.error("failed to save registration data", "CACHE_ERROR");
            }

            return ResendResult.success("verification code resent successfully", verificationType);

        } catch (Exception error) {
            log.error("Error resending verification: {}", error.getMessage());
            return ResendResult.error("failed to resend verification code", "INTERNAL_ERROR");
        }
    }

    private String generateVerificationCode() {
        return String.format("%06d", (int) (Math.random() * 1000000));
    }
}
