package com.aetrust.controllers;

import com.aetrust.dto.RequestDTOs.*;
import com.aetrust.dto.ApiResponse;
import com.aetrust.services.AuthService;
import com.aetrust.services.SecurityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Map;
import java.util.HashMap;

@Slf4j
@RestController
@RequestMapping("/api/v1/auth")
@Validated
public class AuthController {
    
    @Autowired
    private AuthService authService;
    
    @Autowired
    private SecurityService securityService;
    
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<Map<String, Object>>> login(
            @Valid @RequestBody LoginRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            String ipAddress = getClientIp(httpRequest);
            String userAgent = httpRequest.getHeader("User-Agent");
            
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                ipAddress, "ip", "/auth/login");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("rate limit exceeded", "RATE_LIMIT_EXCEEDED"));
            }
            
            SecurityService.BruteForceResult bruteForce = securityService.checkBruteForceProtection(
                request.getEmail(), "user");
            
            if (!bruteForce.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error(bruteForce.getReason(), "ACCOUNT_LOCKED"));
            }
            
            AuthService.LoginResult result = authService.login(request, ipAddress, userAgent);
            
            if (!result.isSuccess()) {
                securityService.recordFailedAttempt(request.getEmail(), "user");
                securityService.recordFailedAttempt(ipAddress, "ip");
                
                return ResponseEntity.status(401).body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            securityService.clearFailedAttempts(request.getEmail(), "user");
            securityService.clearFailedAttempts(ipAddress, "ip");
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("access_token", result.getAccessToken());
            responseData.put("refresh_token", result.getRefreshToken());
            responseData.put("user", result.getUserData());
            responseData.put("expires_in", 3600);
            responseData.put("token_type", "Bearer");
            
            if (result.isRequires2FA()) {
                responseData.put("requires_2fa", true);
                responseData.put("challenge_token", result.getChallengeToken());
            }
            
            return ResponseEntity.ok(
                ApiResponse.success("login successful", responseData));
                
        } catch (Exception error) {
            log.error("Error during login: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("login failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<Map<String, Object>>> refreshToken(
            @RequestBody Map<String, String> request,
            HttpServletRequest httpRequest) {
        
        try {
            String refreshToken = request.get("refresh_token");
            if (refreshToken == null || refreshToken.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error("refresh token is required", "MISSING_REFRESH_TOKEN"));
            }
            
            AuthService.RefreshResult result = authService.refreshToken(refreshToken);
            
            if (!result.isSuccess()) {
                return ResponseEntity.status(401).body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("access_token", result.getAccessToken());
            responseData.put("refresh_token", result.getRefreshToken());
            responseData.put("expires_in", 3600);
            responseData.put("token_type", "Bearer");
            
            return ResponseEntity.ok(
                ApiResponse.success("token refreshed successfully", responseData));
                
        } catch (Exception error) {
            log.error("Error refreshing token: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("token refresh failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<Map<String, Object>>> logout(
            @RequestBody Map<String, String> request,
            HttpServletRequest httpRequest) {
        
        try {
            String accessToken = request.get("access_token");
            String refreshToken = request.get("refresh_token");
            
            AuthService.LogoutResult result = authService.logout(accessToken, refreshToken);
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("logged_out", true);
            
            return ResponseEntity.ok(
                ApiResponse.success("logout successful", responseData));
                
        } catch (Exception error) {
            log.error("Error during logout: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("logout failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/forgot-password")
    public ResponseEntity<ApiResponse<Map<String, Object>>> forgotPassword(
            @RequestBody Map<String, String> request,
            HttpServletRequest httpRequest) {
        
        try {
            String email = request.get("email");
            if (email == null || email.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error("email is required", "MISSING_EMAIL"));
            }
            
            String ipAddress = getClientIp(httpRequest);
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                ipAddress, "ip", "/auth/forgot-password");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("rate limit exceeded", "RATE_LIMIT_EXCEEDED"));
            }
            
            AuthService.ForgotPasswordResult result = authService.forgotPassword(email);
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("email_sent", result.isEmailSent());
            responseData.put("reset_token_expires", 900); // 15 minutes
            
            return ResponseEntity.ok(
                ApiResponse.success("password reset email sent", responseData));
                
        } catch (Exception error) {
            log.error("Error in forgot password: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("forgot password failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/reset-password")
    public ResponseEntity<ApiResponse<Map<String, Object>>> resetPassword(
            @RequestBody Map<String, String> request,
            HttpServletRequest httpRequest) {
        
        try {
            String token = request.get("token");
            String newPassword = request.get("new_password");
            
            if (token == null || newPassword == null) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error("token and new password are required", "MISSING_FIELDS"));
            }
            
            AuthService.ResetPasswordResult result = authService.resetPassword(token, newPassword);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("password_reset", true);
            
            return ResponseEntity.ok(
                ApiResponse.success("password reset successful", responseData));
                
        } catch (Exception error) {
            log.error("Error resetting password: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("password reset failed", "INTERNAL_ERROR"));
        }
    }
    
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> healthCheck() {
        Map<String, Object> healthData = new HashMap<>();
        healthData.put("status", "ok");
        healthData.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(
            ApiResponse.success("auth service is running", healthData));
    }
    
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
