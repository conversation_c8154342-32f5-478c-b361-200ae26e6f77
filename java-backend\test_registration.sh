#!/bin/bash

# Test script for AeTrust registration endpoint

echo "Testing AeTrust Registration Endpoint..."
echo "========================================"

# Test 1: Initial Registration
echo "Step 1: Testing Initial Registration..."
curl -X POST http://localhost:3000/api/v1/auth/registration/initiate \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "phone": "+250788123456",
    "firstName": "<PERSON>",
    "lastName": "Doe",
    "password": "SecurePass123!",
    "userType": "individual",
    "platform": "web"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n\n"

# Test 2: Health Check
echo "Step 2: Testing Health Check..."
curl -X GET http://localhost:3000/api/v1/health \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n\n"

# Test 3: Agent Registration
echo "Step 3: Testing Agent Registration..."
curl -X POST http://localhost:3000/api/v1/auth/registration/initiate \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "phone": "+250788123457",
    "firstName": "Jane",
    "lastName": "Smith",
    "password": "AgentPass123!",
    "userType": "agent",
    "platform": "web"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n\n"

# Test 4: Business Registration
echo "Step 4: Testing Business Registration..."
curl -X POST http://localhost:3000/api/v1/auth/registration/initiate \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "phone": "+250788123458",
    "firstName": "Business",
    "lastName": "Owner",
    "password": "BusinessPass123!",
    "userType": "business",
    "platform": "web"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n\n"

echo "Testing completed!"
echo "Expected HTTP Status: 200 for successful requests"
echo "Check the JSON responses above for success/error details"
